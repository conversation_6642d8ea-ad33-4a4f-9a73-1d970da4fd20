@import "tailwindcss";

:root {
  --background: #0a0a0a;
  --foreground: #fafafa;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  overflow-x: hidden;
  font-family: var(--font-geist-sans);
}

/* Scroll animations */
.fade-in-section {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in-section.is-visible {
  opacity: 1;
  transform: translateY(0);
}

/* Staggered animation delays */
.delay-100 { transition-delay: 100ms; }
.delay-200 { transition-delay: 200ms; }
.delay-300 { transition-delay: 300ms; }
.delay-400 { transition-delay: 400ms; }

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Font utility classes */
.heading {
  font-family: var(--font-nova-square);
}

.mono {
  font-family: var(--font-geist-mono);
}


